// pages/activity/list/index.js
const api = require('../../../config/api.js');
const userUtils = require('../../../utils/user');
const app = getApp();
const systemInfoService = require('../../../services/systemInfo.js');

Page({
  data: {
    searchValue: '',
    app: getApp(),
    // 登录状态
    hasLogin: false,
    // 活动列表数据
    activities: [],
    // 分页参数
    pageNum: 1,
    pageSize: 10,
    hasMore: true,
    loading: false,
    // 分类数据
    categories: [],
    // 筛选参数
    activeTab: 0, // 激活的标签页，0: 全部, 1: 文化活动, 2: 体育活动, 3: 教育活动, 4: 公益活动, 5: 娱乐活动
    selectedCategory: '', // 选中的分类
    // 页面状态控制
    isFirstLoad: true, // 是否是首次加载
  },

  onLoad() {
    // 获取分类数据
    this.loadCategories()

    // 确保系统信息已加载
    this.ensureSystemInfo()

    // 直接加载活动数据
    this.loadActivities(true)
  },

  onUnload() {
    // 页面卸载时的清理工作
  },

  onShow() {
    console.log('活动页面 onShow 触发')

    // 如果不是首次加载，说明是从其他页面返回
    if (!this.data.isFirstLoad) {
      console.log('从其他页面返回')

      // 如果已经有数据，就不重新加载，保持当前滚动位置
      if (this.data.activities.length > 0) {
        console.log('已有数据，跳过重新加载')
        return
      }

      // 只有在没有数据时才加载数据
      console.log('无数据，加载数据')
      this.loadActivities(true)
    }

    // 标记为非首次加载
    this.setData({
      isFirstLoad: false
    })
  },

  // 确保系统信息已加载
  async ensureSystemInfo() {
    try {
      let systemInfo = wx.getStorageSync('systemInfo')
      if (!systemInfo || !systemInfo.fileUrl) {
        const res = await api.systemInfo()
        if (res && res.code === 200) {
          systemInfo = res.data
          wx.setStorageSync('systemInfo', systemInfo)
        }
      }
    } catch (error) {
      // 获取系统信息失败时使用默认值
    }
  },

  // 加载分类数据（使用统一的系统信息服务）
  async loadCategories() {
    try {
      // 使用统一的系统信息服务获取格式化的活动分类数据
      const categories = await systemInfoService.getFormattedActivityCategories()

      // 转换数据格式以适配现有的UI组件
      const formattedCategories = categories.map(item => ({
        value: item.id === 'all' ? '' : item.id,
        text: item.name
      }))

      this.setData({
        categories: formattedCategories
      })
    } catch (error) {
      console.error('加载活动分类数据失败:', error)

      // 设置空的分类数据
      this.setData({
        categories: []
      })

      // 显示错误提示
      wx.showToast({
        title: '分类加载失败，请重试',
        icon: 'none',
        duration: 2000
      })
    }
  },

  // 加载活动列表
  async loadActivities(e) {
    // 支持通过事件参数或直接传入布尔值来决定是否刷新
    const refresh = e && e.currentTarget ? e.currentTarget.dataset.refresh === "true" : !!e;

    // 防止重复加载
    if (this.data.loading) {
      console.log('正在加载中，跳过重复请求')
      return;
    }

    try {
      this.setData({ loading: true })

      if (refresh) {
        this.setData({
          pageNum: 1,
          hasMore: true,
          activities: []
        })
      }

      // 构建查询参数
      const params = {
        pageNum: this.data.pageNum,
        pageSize: this.data.pageSize
      }

      // 添加搜索关键字
      if (this.data.searchValue) {
        params.title = this.data.searchValue
      }

      // 添加分类筛选
      if (this.data.selectedCategory) {
        params.category = this.data.selectedCategory
      }

      // 调用接口获取活动列表
      const res = await api.getActivityList(params)

      // 检查API响应
      if (!res) {
        throw new Error('服务器未返回数据')
      }

      // 统一的数据结构处理
      const rows = res.rows || [];
      const total = res.total || 0;

      if (rows.length === 0) {
        this.setData({
          activities: refresh ? [] : this.data.activities,
          hasMore: false
        });
        return;
      }

      const hasMore = this.data.pageNum * this.data.pageSize < total;

      // 使用系统信息服务格式化数据
      const formattedActivities = await Promise.all(rows.map(async (item) => {
        try {
          // 格式化时间，添加错误处理
          let formattedStartTime = '';
          try {
            if (item.startTime) {
              // 使用iOS兼容的日期格式
              let timeStr = item.startTime;
              if (timeStr.includes('-')) {
                timeStr = timeStr.replace(/-/g, '/');
              }
              const startTime = new Date(timeStr);

              if (isNaN(startTime.getTime())) {
                throw new Error('无效的日期时间');
              }

              formattedStartTime = `${startTime.getMonth() + 1}月${startTime.getDate()}日 ${startTime.getHours()}:${startTime.getMinutes().toString().padStart(2, '0')}`;
            }
          } catch (timeError) {
            formattedStartTime = item.startTime || '时间未知';
          }

          // 使用系统信息服务处理图片
          const imageResult = await systemInfoService.processImageUrls(item.images || '')

          // 确保地点名称
          if (!item.locationName && item.location) {
            item.locationName = '查看地点'
          }

          // 确保状态名称
          const statusMap = {
            '0': '未开始',
            '1': '进行中',
            '2': '已结束'
          }
          if (!item.statusName && item.status) {
            item.statusName = statusMap[item.status] || '未知状态'
          }

          return {
            ...item,
            formattedStartTime,
            imageUrl: imageResult.imageUrl,
            images: imageResult.images
          }
        } catch (error) {
          console.error('格式化活动数据失败:', error)
          return {
            ...item,
            formattedStartTime: item.startTime || '时间未知',
            imageUrl: '',
            images: []
          }
        }
      }))

      // 更新数据
      this.setData({
        activities: refresh ? formattedActivities : [...this.data.activities, ...formattedActivities],
        hasMore: hasMore,
        pageNum: this.data.pageNum + 1
      })
    } catch (error) {
      console.error('加载活动列表失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    } finally {
      // 添加延迟关闭刷新动画，提供更好的用户体验
      setTimeout(() => {
        this.setData({ loading: false })
        wx.stopPullDownRefresh && wx.stopPullDownRefresh()
      }, 800)
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadActivities(true)
  },

  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadActivities()
    }
  },

  // 搜索框输入变化
  onSearchChange(e) {
    this.setData({
      searchValue: e.detail
    })
  },

  // 搜索框搜索事件
  onSearch() {
    this.loadActivities(true)
  },

  // 分类切换
  onCategoryChange(e) {
    const index = e.currentTarget.dataset.index
    const category = this.data.categories[index].value

    this.setData({
      activeTab: index,
      selectedCategory: category
    })

    this.loadActivities(true)
  },

  // 点击活动卡片
  onActivityTap(e) {
    const activityId = e.currentTarget.dataset.id

    if (!activityId) {
      wx.showToast({
        title: '活动信息不完整',
        icon: 'none'
      });
      return;
    }

    // 直接跳转到活动详情页面，无需登录检查
    wx.navigateTo({
      url: `/pages/activity/detail/index?id=${activityId}`,
      fail: (err) => {
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 图片加载错误处理
  onImageError(e) {
    const index = e.currentTarget.dataset.index;

    // 获取当前活动列表
    const activities = this.data.activities;
    if (activities[index]) {
      // 更新当前活动的图片为默认图片
      // 注意：这里不直接修改数组中的对象，而是创建一个新的数组
      const updatedActivities = [...activities];
      updatedActivities[index] = {
        ...updatedActivities[index],
        imageUrl: '', // 清空图片URL，让模板使用默认图片
        images: ''    // 同时清空images字段
      };

      // 更新数据，触发视图更新
      this.setData({
        activities: updatedActivities
      });
    }
  },

  // 确保登录并执行回调的辅助方法
  ensureLogin(callback) {
    return userUtils.ensureLogin(this, callback)
  },

  // 点击底部 tabBar 活动按钮时触发
  onTabItemTap: function(item) {
    // 如果已经在活动页面，重新刷新数据
    if (item.pagePath === 'pages/activity/list/index') {
      console.log('重新刷新活动页面数据')

      // 防止重复加载：如果正在加载中，则跳过
      if (this.data.loading) {
        console.log('正在加载中，跳过重复刷新')
        return
      }

      // 重置搜索状态
      this.setData({
        searchValue: '',
        activeTab: 0,
        selectedCategory: ''
      })

      // 重新加载活动数据
      this.loadActivities(true)
    }
  },

  // 发布活动
  onPublishActivity() {
    // 检查小区认证状态
    userUtils.ensureResidentialAuth(this, () => {
      // 小区认证通过，跳转到发布页面
      wx.navigateTo({
        url: '/pages/activity-publish/index'
      })
    })
  },

  // 登录成功回调
  onLoginSuccess() {
    console.log('活动列表页面：用户登录成功')
    // 登录成功后可以进行一些操作，比如刷新数据等
    // 这里暂时不需要特殊处理
  }
})
